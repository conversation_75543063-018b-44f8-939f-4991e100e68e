#ifndef V4L2_CAPTURE_INTERFACE_H
#define V4L2_CAPTURE_INTERFACE_H

#include "../common.h"
#include "../config/v4l2_capture_config.h"
#include "../transport/video_transport_interface.h"
#include <linux/videodev2.h>
#include <sys/mman.h>
#include <functional>
#include <vector>
#include <memory>
#include <queue>
#include <mutex>
#include <atomic>

// Constants
#ifndef VIDEO_MAX_PLANES
#define VIDEO_MAX_PLANES 8  // Maximum number of planes supported by V4L2
#endif

/**
 * Pure V4L2 Capture Interface
 * 
 * This interface ONLY handles V4L2 driver interactions:
 * - Device management (open/close/configure)
 * - Buffer allocation and management (DMABUF/MMAP)
 * - Streaming control (STREAMON/STREAMOFF)
 * - Frame capture (QBUF/DQBUF)
 * 
 * It does NOT handle:
 * - Memory distribution to other processes
 * - Frame format conversion
 * - Transport mechanisms
 */

namespace V4L2Capture {

// Forward declarations
struct V4L2Buffer;
struct V4L2Frame;

// V4L2平面信息（支持多平面格式）
struct V4L2Plane {
    size_t size;                        // 平面缓冲区大小
    void* addr;                         // 映射地址 (MMAP模式)
    int dma_fd;                         // DMA文件描述符 (DMABUF模式)
    size_t bytes_used;                  // 实际使用字节数
    size_t offset;                      // 平面偏移量
};

// V4L2缓冲区信息（支持多平面）
struct V4L2Buffer {
    uint32_t index;                     // V4L2缓冲区索引
    uint32_t num_planes;                // 平面数量
    std::vector<V4L2Plane> planes;      // 平面信息数组
    uint64_t timestamp_us;              // 时间戳
    uint32_t sequence;                  // 序列号
    bool is_multiplanar;                // 是否为多平面格式
    video_transport::TransportType transport_type; // 传输类型（DMA/SHMEM/FASTDDS）
};

struct V4L2Frame {
    V4L2Buffer buffer;                  // 缓冲区信息（包含所有数据，不包含元数据）
    video_transport::IVideoPublisher::PublisherData publisher_data;
};

// V4L2捕获设备接口（纯V4L2操作）
class IV4L2CaptureDevice {
public:
    virtual ~IV4L2CaptureDevice() = default;
    
    // 设备管理
    virtual bool open_device(const std::string& device_path) = 0;
    virtual void close_device() = 0;
    virtual bool is_device_open() const = 0;
    
    // 格式设置
    virtual bool set_format(uint32_t width, uint32_t height, uint32_t pixel_format) = 0;
    virtual bool get_format(uint32_t& width, uint32_t& height, uint32_t& pixel_format) = 0;
    
    // 帧率控制
    virtual bool set_framerate(uint32_t fps) = 0;
    virtual bool get_framerate(uint32_t& fps) = 0;
    
    // 缓冲区管理
    virtual bool allocate_buffers(uint32_t count, bool use_dmabuf = false, 
                                   video_transport::IVideoPublisher* video_publisher = nullptr) = 0;
    virtual bool deallocate_buffers() = 0;
    virtual uint32_t get_buffer_count() const = 0;
    
    // 流控制
    virtual bool start_streaming() = 0;
    virtual bool stop_streaming() = 0;
    virtual bool is_streaming() const = 0;
    
    // 帧捕获（纯V4L2操作，不涉及传输）
    virtual bool capture_frame(V4L2Frame& frame, int timeout_ms = 1000) = 0;
    virtual bool release_frame(const V4L2Frame& frame) = 0;
    
    // 状态查询
    virtual std::string get_device_info() const = 0;
    virtual bool get_capability(v4l2_capability& cap) const = 0;
};

// V4L2捕获设备实现
class V4L2CaptureDevice : public IV4L2CaptureDevice {
private:
    int fd_;
    std::string device_path_;
    bool is_streaming_;
    bool use_dmabuf_;
    bool is_multiplanar_;
    
    // 格式信息
    uint32_t width_;
    uint32_t height_;
    uint32_t pixel_format_;
    uint32_t fps_;
    
    // 缓冲区管理
    std::vector<V4L2Buffer> buffers_;
    std::queue<uint32_t> available_buffers_;  // 可用缓冲区索引
    std::queue<uint32_t> queued_buffers_;     // 已入队缓冲区索引
    mutable std::mutex buffers_mutex_;
    
    // 视频发布者（外部提供）
    video_transport::IVideoPublisher* video_publisher_;
    
    // 统计信息
    std::atomic<uint64_t> frames_captured_ = 0;
    std::atomic<uint64_t> frames_dropped_ = 0;

public:
    V4L2CaptureDevice();
    virtual ~V4L2CaptureDevice();
    
    // IV4L2CaptureDevice接口实现
    bool open_device(const std::string& device_path) override;
    void close_device() override;
    bool is_device_open() const override { return fd_ >= 0; }
    
    bool set_format(uint32_t width, uint32_t height, uint32_t pixel_format) override;
    bool get_format(uint32_t& width, uint32_t& height, uint32_t& pixel_format) override;
    
    bool set_framerate(uint32_t fps) override;
    bool get_framerate(uint32_t& fps) override;
    
    bool allocate_buffers(uint32_t count, bool use_dmabuf = false, 
                         video_transport::IVideoPublisher* video_publisher = nullptr) override;
    bool deallocate_buffers() override;
    uint32_t get_buffer_count() const override { return buffers_.size(); }
    
    bool start_streaming() override;
    bool stop_streaming() override;
    bool is_streaming() const override { return is_streaming_; }
    
    bool capture_frame(V4L2Frame& frame, int timeout_ms = 1000) override;
    bool release_frame(const V4L2Frame& frame) override;
    
    std::string get_device_info() const override;
    bool get_capability(v4l2_capability& cap) const override;
    
    // 统计信息
    uint64_t get_frames_captured() const { return frames_captured_.load(); }
    uint64_t get_frames_dropped() const { return frames_dropped_.load(); }

private:
    // V4L2底层操作
    bool query_capability();
    bool set_format_internal();
    bool set_framerate_internal();
    
    // 缓冲区相关
    bool queue_all_buffers();
    bool queue_buffer(uint32_t index);
    bool dequeue_buffer(V4L2Buffer& buffer);
    
    // 工具函数
    bool wait_for_frame(int timeout_ms);
    void cleanup_buffers();
    std::string format_to_string(uint32_t format) const;
};

// V4L2捕获设备实现
inline V4L2CaptureDevice::V4L2CaptureDevice()
    : fd_(-1), is_streaming_(false), use_dmabuf_(false), is_multiplanar_(false),
      width_(0), height_(0), pixel_format_(0), fps_(0),
      video_publisher_(nullptr) {
}

inline V4L2CaptureDevice::~V4L2CaptureDevice() {
    close_device();
}

inline bool V4L2CaptureDevice::open_device(const std::string& device_path) {
    if (fd_ >= 0) {
        close_device();
    }
    
    device_path_ = device_path;
    fd_ = open(device_path.c_str(), O_RDWR | O_NONBLOCK);
    
    if (fd_ < 0) {
        LOG_E("Failed to open V4L2 device %s: %s", device_path.c_str(), strerror(errno));
        return false;
    }
    
    if (!query_capability()) {
        close_device();
        return false;
    }
    
    LOG_I("V4L2 device %s opened successfully", device_path.c_str());
    return true;
}

inline void V4L2CaptureDevice::close_device() {
    if (fd_ >= 0) {
        stop_streaming();
        deallocate_buffers();
        close(fd_);
        fd_ = -1;
        device_path_.clear();
    }
}

inline bool V4L2CaptureDevice::query_capability() {
    v4l2_capability cap;
    if (ioctl(fd_, VIDIOC_QUERYCAP, &cap) < 0) {
        LOG_E("Failed to query V4L2 capability: %s", strerror(errno));
        return false;
    }
    
    // 检查是否支持视频捕获（单平面或多平面）
    bool supports_capture = false;
    if (cap.capabilities & V4L2_CAP_VIDEO_CAPTURE) {
        is_multiplanar_ = false;
        supports_capture = true;
    } else if (cap.capabilities & V4L2_CAP_VIDEO_CAPTURE_MPLANE) {
        is_multiplanar_ = true;
        supports_capture = true;
    }
    
    if (!supports_capture) {
        LOG_E("Device does not support video capture");
        return false;
    }
    
    if (!(cap.capabilities & V4L2_CAP_STREAMING)) {
        LOG_E("Device does not support streaming");
        return false;
    }
    
    LOG_I("V4L2 device: %s, driver: %s, version: %u.%u.%u, multiplanar: %s",
          cap.card, cap.driver,
          (cap.version >> 16) & 0xFF,
          (cap.version >> 8) & 0xFF,
          cap.version & 0xFF,
          is_multiplanar_ ? "Yes" : "No");
    
    return true;
}

inline bool V4L2CaptureDevice::set_format(uint32_t width, uint32_t height, uint32_t pixel_format) {
    if (fd_ < 0) {
        LOG_E("Device not opened");
        return false;
    }
    
    width_ = width;
    height_ = height;
    pixel_format_ = pixel_format;
    
    return set_format_internal();
}

inline bool V4L2CaptureDevice::set_format_internal() {
    v4l2_format fmt;
    memset(&fmt, 0, sizeof(fmt));
    
    if (is_multiplanar_) {
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
        fmt.fmt.pix_mp.width = width_;
        fmt.fmt.pix_mp.height = height_;
        fmt.fmt.pix_mp.pixelformat = pixel_format_;
        fmt.fmt.pix_mp.field = V4L2_FIELD_INTERLACED;
        fmt.fmt.pix_mp.num_planes = 1; // 大多数格式使用1个平面，某些YUV格式可能需要更多
    } else {
        fmt.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        fmt.fmt.pix.width = width_;
        fmt.fmt.pix.height = height_;
        fmt.fmt.pix.pixelformat = pixel_format_;
        fmt.fmt.pix.field = V4L2_FIELD_INTERLACED;
    }
    
    if (ioctl(fd_, VIDIOC_S_FMT, &fmt) < 0) {
        LOG_E("Failed to set V4L2 format: %s", strerror(errno));
        return false;
    }
    
    // 验证设置的格式
    uint32_t actual_width, actual_height, actual_format;
    if (is_multiplanar_) {
        actual_width = fmt.fmt.pix_mp.width;
        actual_height = fmt.fmt.pix_mp.height;
        actual_format = fmt.fmt.pix_mp.pixelformat;
    } else {
        actual_width = fmt.fmt.pix.width;
        actual_height = fmt.fmt.pix.height;
        actual_format = fmt.fmt.pix.pixelformat;
    }
    
    if (actual_width != width_ || actual_height != height_ || actual_format != pixel_format_) {
        LOG_W("V4L2 format adjusted: %ux%u %s -> %ux%u %s",
              width_, height_, format_to_string(pixel_format_).c_str(),
              actual_width, actual_height, format_to_string(actual_format).c_str());
        
        width_ = actual_width;
        height_ = actual_height;
        pixel_format_ = actual_format;
    }
    
    LOG_I("V4L2 format set: %ux%u %s (multiplanar: %s)", 
          width_, height_, format_to_string(pixel_format_).c_str(),
          is_multiplanar_ ? "Yes" : "No");
    return true;
}

inline bool V4L2CaptureDevice::get_format(uint32_t& width, uint32_t& height, uint32_t& pixel_format) {
    width = width_;
    height = height_;
    pixel_format = pixel_format_;
    return true;
}

inline bool V4L2CaptureDevice::set_framerate(uint32_t fps) {
    fps_ = fps;
    return set_framerate_internal();
}

inline bool V4L2CaptureDevice::set_framerate_internal() {
    if (fd_ < 0) {
        LOG_E("Device not opened");
        return false;
    }
    
    v4l2_streamparm parm;
    memset(&parm, 0, sizeof(parm));
    parm.type = is_multiplanar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE;
    parm.parm.capture.timeperframe.numerator = 1;
    parm.parm.capture.timeperframe.denominator = fps_;
    
    if (ioctl(fd_, VIDIOC_S_PARM, &parm) < 0) {
        LOG_W("Failed to set framerate to %u fps: %s", fps_, strerror(errno));
        return false;
    }
    
    LOG_I("V4L2 framerate set to %u fps", fps_);
    return true;
}

inline bool V4L2CaptureDevice::get_framerate(uint32_t& fps) {
    fps = fps_;
    return true;
}

inline bool V4L2CaptureDevice::allocate_buffers(uint32_t count, bool use_dmabuf, video_transport::IVideoPublisher* video_publisher) {
    if (fd_ < 0) {
        LOG_E("Device not opened");
        return false;
    }
    
    use_dmabuf_ = use_dmabuf;
    video_publisher_ = video_publisher;
    
    if (!video_publisher) {
        LOG_E("Video publisher is required for DMABUF mode");
        return false;
    }

    if (use_dmabuf) {
        if (!video_publisher->supports_v4l2_zero_copy()) {
            LOG_E("Video publisher does not support V4L2 zero-copy");
            return false;
        }
    }

    uint32_t req_count = count;
    int req_type = is_multiplanar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE;
    int req_mem = use_dmabuf ? V4L2_MEMORY_DMABUF : V4L2_MEMORY_MMAP;
    
    v4l2_requestbuffers req;
    memset(&req, 0, sizeof(req));
    req.count = req_count;
    req.type = req_type;
    req.memory = req_mem;
    
    if (ioctl(fd_, VIDIOC_REQBUFS, &req) < 0) {
        LOG_E("Failed to request MMAP buffers: %s", strerror(errno));
        return false;
    }
    
    LOG_W("Request %u buffers, allocated %u buffers", req_count, req.count);
    
    buffers_.resize(req.count);
    
    // 映射缓冲区
    for (uint32_t i = 0; i < req.count; i++) {
        v4l2_buffer buf;
        memset(&buf, 0, sizeof(buf));
        buf.type = req_type;
        buf.memory = req_mem;
        buf.index = i;
        
        v4l2_plane planes[VIDEO_MAX_PLANES];
        memset(planes, 0, sizeof(planes));
        if (is_multiplanar_) {
            buf.m.planes = planes;
            buf.length = VIDEO_MAX_PLANES;
        }
        
        if (ioctl(fd_, VIDIOC_QUERYBUF, &buf) < 0) {
            LOG_E("Failed to query buffer %u: %s", i, strerror(errno));
            return false;
        }

        if (is_multiplanar_) {
            for (uint32_t j = 0; j < buf.length; j++) {
                LOG_I("Buffer %u plane %u: size %u, offset %u", i, j, planes[j].length, planes[j].m.mem_offset);
            }
        } else {
            LOG_I("Buffer %u: size %u, offset %u", i, buf.length, buf.m.offset);
        }

        // 从视频发布者获取缓冲区句柄
        video_transport::IVideoPublisher::PublisherData pub_data;
        auto result = video_publisher_->acquire_buffer(pub_data);
        if (result != video_transport::BufferResult::SUCCESS) {
            LOG_E("Failed to acquire buffer handle %u from video publisher", i);
            return false;
        }
        
        V4L2Buffer& cache_buf = buffers_[i]; 
        cache_buf.index = i;
        cache_buf.is_multiplanar = is_multiplanar_;
        cache_buf.transport_type = pub_data.transport_type;

        if (is_multiplanar_) {
            // 多平面格式, 只取第一个平面, 其他平面由用户自己处理
            cache_buf.num_planes = 1;
            cache_buf.planes.resize(1);
            cache_buf.planes[0].size = planes[0].length;
            cache_buf.planes[0].offset = planes[0].m.mem_offset;
            if (use_dmabuf_) {
                cache_buf.planes[0].dma_fd = video_publisher_->get_dma_fd(pub_data);
                cache_buf.planes[0].addr = nullptr;
                if (cache_buf.planes[0].dma_fd < 0) {
                    LOG_E("Failed to get DMA fd for buffer %u plane %u", i, 0);
                    return false;
                }
            } else {
                cache_buf.planes[0].dma_fd = -1;
                cache_buf.planes[0].addr = mmap(nullptr, planes[0].length, 
                                                 PROT_READ | PROT_WRITE, MAP_SHARED, 
                                                 fd_, planes[0].m.mem_offset);
                if (cache_buf.planes[0].addr == MAP_FAILED) {
                    LOG_E("Failed to mmap buffer %u plane %u: %s", i, 0, strerror(errno));
                    return false;
                }
            }
        } else {
            cache_buf.num_planes = 1;
            cache_buf.planes.resize(1);
            cache_buf.planes[0].size = buf.length;
            cache_buf.planes[0].offset = buf.m.offset;
            if (use_dmabuf_) {
                cache_buf.planes[0].dma_fd = video_publisher_->get_dma_fd(pub_data);
                cache_buf.planes[0].addr = nullptr;
                if (cache_buf.planes[0].dma_fd < 0) {
                    LOG_E("Failed to get DMA fd for buffer %u plane %u", i, 0);
                    return false;
                }
            } else {
                cache_buf.planes[0].dma_fd = -1;
                cache_buf.planes[0].addr = mmap(nullptr, buf.length, PROT_READ | PROT_WRITE,
                                                MAP_SHARED, fd_, buf.m.offset);
                
                if (cache_buf.planes[0].addr == MAP_FAILED) {
                    LOG_E("Failed to mmap buffer %u: %s", i, strerror(errno));
                    return false;
                }
            }
        }
        LOG_I("Allocated buffer %u, size: %u, addr: %p, dma_fd: %d", i, cache_buf.planes[0].size, cache_buf.planes[0].addr, cache_buf.planes[0].dma_fd);
        available_buffers_.push(i);
    }
    
    LOG_I("Allocated %u buffers with %s format", req.count, 
          is_multiplanar_ ? "multi-planar" : "single-planar");
    return queue_all_buffers();
}

inline bool V4L2CaptureDevice::queue_all_buffers() {
    std::lock_guard<std::mutex> lock(buffers_mutex_);
    
    while (!available_buffers_.empty()) {
        uint32_t index = available_buffers_.front();
        available_buffers_.pop();
        
        if (queue_buffer(index)) {
            queued_buffers_.push(index);
        } else {
            available_buffers_.push(index);
            return false;
        }
    }
    
    LOG_I("Queued %zu buffers to V4L2 driver", buffers_.size());
    return true;
}

inline bool V4L2CaptureDevice::queue_buffer(uint32_t index) {
    V4L2Buffer& cache_buf = buffers_[index];  // 缓存缓冲区信息，提高性能
    v4l2_buffer buf;
    memset(&buf, 0, sizeof(buf));
    buf.type = is_multiplanar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE;
    buf.index = index;
    buf.memory = use_dmabuf_ ? V4L2_MEMORY_DMABUF : V4L2_MEMORY_MMAP;

    v4l2_plane planes[VIDEO_MAX_PLANES];
    if (is_multiplanar_) {
        memset(planes, 0, sizeof(planes));
        buf.m.planes = planes;
        buf.length = cache_buf.num_planes;
        
        for (uint32_t i = 0; i < cache_buf.num_planes; i++) {
            if (use_dmabuf_) {
                // 使用视频发布者获取DMA文件描述符
                planes[i].m.fd = cache_buf.planes[i].dma_fd;
            }
            planes[i].length = cache_buf.planes[i].size;
            planes[i].data_offset = 0;
            planes[i].bytesused = 0;  // 初始化为0
        }
    } else {
        if (use_dmabuf_) {
            buf.m.fd = cache_buf.planes[0].dma_fd;
        }
        buf.length = cache_buf.planes[0].size;
        buf.bytesused = 0;  // 初始化为0
    }
    
    if (ioctl(fd_, VIDIOC_QBUF, &buf) < 0) {
        LOG_E("Failed to queue buffer %u: %s", index, strerror(errno));
        return false;
    }
    
    return true;
}

inline bool V4L2CaptureDevice::start_streaming() {
    if (fd_ < 0) {
        LOG_E("Device not opened");
        return false;
    }
    
    enum v4l2_buf_type type = is_multiplanar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE;
    if (ioctl(fd_, VIDIOC_STREAMON, &type) < 0) {
        LOG_E("Failed to start streaming: %s", strerror(errno));
        return false;
    }
    
    is_streaming_ = true;
    LOG_I("V4L2 streaming started (%s)", is_multiplanar_ ? "multi-planar" : "single-planar");
    return true;
}

inline bool V4L2CaptureDevice::stop_streaming() {
    if (fd_ < 0 || !is_streaming_) {
        return true;
    }
    
    enum v4l2_buf_type type = is_multiplanar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE;
    if (ioctl(fd_, VIDIOC_STREAMOFF, &type) < 0) {
        LOG_E("Failed to stop streaming: %s", strerror(errno));
        return false;
    }
    
    is_streaming_ = false;
    LOG_I("V4L2 streaming stopped");
    return true;
}

inline bool V4L2CaptureDevice::capture_frame(V4L2Frame& frame, int timeout_ms) {
    if (!is_streaming_) {
        LOG_E("Streaming not started");
        return false;
    }
    
    // 等待帧数据
    if (!wait_for_frame(timeout_ms)) {
        return false;
    }
    
    // 从V4L2驱动出队缓冲区
    V4L2Buffer buffer;
    if (!dequeue_buffer(buffer)) {
        return false;
    }
    
    // 填充帧信息
    frame.buffer = buffer;
    frame.publisher_data.data_ptr = buffer.planes[0].addr;
    frame.publisher_data.buffer_id = buffer.index;
    frame.publisher_data.transport_type = buffer.transport_type;

    //填充metadata
    frame.publisher_data.meta.frame_id = buffer.sequence;
    frame.publisher_data.meta.width = width_;
    frame.publisher_data.meta.height = height_;
    frame.publisher_data.meta.format = pixel_format_;
    frame.publisher_data.meta.data_size = buffer.planes[0].bytes_used;
    frame.publisher_data.meta.timestamp = buffer.timestamp_us;
    frame.publisher_data.meta.is_keyframe = true;
    frame.publisher_data.meta.is_valid = true;
    
    frames_captured_.fetch_add(1);
    return true;
}

inline bool V4L2CaptureDevice::release_frame(const V4L2Frame& frame) {
    std::lock_guard<std::mutex> lock(buffers_mutex_);
    
    // 重新入队缓冲区
    if (queue_buffer(frame.buffer.index)) {
        queued_buffers_.push(frame.buffer.index);
        return true;
    }
    
    return false;
}

inline bool V4L2CaptureDevice::wait_for_frame(int timeout_ms) {
    fd_set fds;
    FD_ZERO(&fds);
    FD_SET(fd_, &fds);
    
    struct timeval tv;
    tv.tv_sec = timeout_ms / 1000;
    tv.tv_usec = (timeout_ms % 1000) * 1000;
    
    int ret = select(fd_ + 1, &fds, nullptr, nullptr, &tv);
    if (ret <= 0) {
        if (ret < 0 && errno != EINTR) {
            LOG_E("V4L2 select error: %s", strerror(errno));
        }
        return false;
    }
    
    return true;
}

inline bool V4L2CaptureDevice::dequeue_buffer(V4L2Buffer& buffer) {
    v4l2_buffer buf;
    memset(&buf, 0, sizeof(buf));
    buf.type = is_multiplanar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE;
    buf.memory = use_dmabuf_ ? V4L2_MEMORY_DMABUF : V4L2_MEMORY_MMAP;
    
    v4l2_plane planes[VIDEO_MAX_PLANES];
    if (is_multiplanar_) {
        memset(planes, 0, sizeof(planes));
        buf.m.planes = planes;
        buf.length = VIDEO_MAX_PLANES;
    }
    
    if (ioctl(fd_, VIDIOC_DQBUF, &buf) < 0) {
        if (errno != EAGAIN) {
            LOG_E("Failed to dequeue buffer: %s", strerror(errno));
        }
        return false;
    }
    
    std::lock_guard<std::mutex> lock(buffers_mutex_);
    
    if (!queued_buffers_.empty()) {
        uint32_t expected_index = queued_buffers_.front();
        queued_buffers_.pop();
        
        if (buf.index != expected_index) {
            LOG_W("Buffer index mismatch: expected %u, got %u", expected_index, buf.index);
        }
        
        buffer = buffers_[buf.index];
        buffer.timestamp_us = get_current_us();
        buffer.sequence = buf.sequence;
        
        // 更新平面数据使用惇况
        if (is_multiplanar_) {
            for (uint32_t i = 0; i < buffer.num_planes && i < buf.length; i++) {
                buffer.planes[i].bytes_used = planes[i].bytesused;
            }
        } else {
            buffer.planes[0].bytes_used = buf.bytesused;
        }
        
        return true;
    }
    
    LOG_E("No queued buffer found");
    return false;
}

inline bool V4L2CaptureDevice::deallocate_buffers() {
    stop_streaming();
    cleanup_buffers();
    
    // 释放V4L2缓冲区
    if (fd_ >= 0) {
        v4l2_requestbuffers req;
        memset(&req, 0, sizeof(req));
        req.count = 0;
        req.type = is_multiplanar_ ? V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE : V4L2_BUF_TYPE_VIDEO_CAPTURE;
        req.memory = use_dmabuf_ ? V4L2_MEMORY_DMABUF : V4L2_MEMORY_MMAP;
        
        ioctl(fd_, VIDIOC_REQBUFS, &req);
    }
    
    return true;
}

inline void V4L2CaptureDevice::cleanup_buffers() {
    for (auto& buffer : buffers_) {
        for (auto& plane : buffer.planes) {
            if (plane.addr && plane.addr != MAP_FAILED) {
                munmap(plane.addr, plane.size);
            }
            // 不直接关闭DMA fd，由IBufferProvider管理
        }
    }
    
    buffers_.clear();
    
    // 清空队列
    std::queue<uint32_t> empty_queue;
    available_buffers_.swap(empty_queue);
    queued_buffers_.swap(empty_queue);
}

inline std::string V4L2CaptureDevice::get_device_info() const {
    std::ostringstream oss;
    oss << "V4L2 Device: " << device_path_ << "\n";
    oss << "Format: " << width_ << "x" << height_ << " " << format_to_string(pixel_format_) << "\n";
    oss << "FPS: " << fps_ << "\n";
    oss << "Buffer Count: " << buffers_.size() << "\n";
    oss << "Buffer Type: " << (use_dmabuf_ ? "DMABUF" : "MMAP") << "\n";
    oss << "Streaming: " << (is_streaming_ ? "Yes" : "No") << "\n";
    oss << "Frames Captured: " << frames_captured_.load() << "\n";
    oss << "Frames Dropped: " << frames_dropped_.load();
    return oss.str();
}

inline bool V4L2CaptureDevice::get_capability(v4l2_capability& cap) const {
    if (fd_ < 0) {
        return false;
    }
    
    return ioctl(fd_, VIDIOC_QUERYCAP, &cap) >= 0;
}

inline std::string V4L2CaptureDevice::format_to_string(uint32_t format) const {
    char fourcc[5] = {0};
    fourcc[0] = format & 0xFF;
    fourcc[1] = (format >> 8) & 0xFF;
    fourcc[2] = (format >> 16) & 0xFF;
    fourcc[3] = (format >> 24) & 0xFF;
    return std::string(fourcc);
}

// V4L2设备工厂
class V4L2DeviceFactory {
public:
    static std::unique_ptr<IV4L2CaptureDevice> create_capture_device() {
        return std::make_unique<V4L2CaptureDevice>();
    }
    
    static std::unique_ptr<IV4L2CaptureDevice> create_and_configure(const V4L2DeviceConfig& config, video_transport::IVideoPublisher* publisher) {
        auto device = create_capture_device();
        
        if (!device->open_device(config.device_path)) {
            return nullptr;
        }
        
        if (!device->set_format(config.width, config.height, config.pixel_format)) {
            return nullptr;
        }
        
        if (!device->set_framerate(config.fps)) {
            LOG_W("Failed to set framerate, continuing anyway");
        }
        
        if (!device->allocate_buffers(config.buffer_count, config.use_dmabuf, publisher)) {
            return nullptr;
        }
        
        return device;
    }
};

} // namespace V4L2Capture

#endif // V4L2_CAPTURE_INTERFACE_H