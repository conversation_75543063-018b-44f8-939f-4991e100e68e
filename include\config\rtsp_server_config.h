#ifndef RTSP_SERVER_CONFIG_H__
#define RTSP_SERVER_CONFIG_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <cstdint>
#include <string>

// RTSP server configuration
struct RTSPServerConfig {
    std::string topic_name = "video_frames";    // DDS input topic name or socket path (for DMA/SHMEM/FASTDDS)
    std::string transport_type = "DMA";        // DMA, SHMEM, FASTDDS

    // Server parameters
    std::string server_address = "0.0.0.0";    // RTSP server bind address
    int server_port = 8554;                    // RTSP server port
    std::string mount_point = "/stream";       // RTSP mount point

    // Output video parameters (keep original size, no scaling)
    int output_fps = 30;
    std::string output_codec = "H264";         // H264, H265
    int output_bitrate = 2000000;              // 2Mbps
    int gop_size = 15;                         // GOP size - set to 1 for immediate frame output

    // Performance optimization parameters
    bool use_hw_encoder_h264 = true;           // H264 hardware encoder
    bool use_hw_encoder_h265 = false;          // H265 hardware encoder
    int max_clients = 10;                      // Maximum clients
    int thread_priority = 85;                  // Thread priority

    // FastDDS config (for DMA/SHMEM)
    int domain_id = 0;                         // FastDDS domain ID (for DMA/SHMEM)
    int max_samples = 5;                       // FastDDS max samples (for DMA/SHMEM)

    // Quality control
    bool adaptive_bitrate = true;              // Adaptive bitrate
    int min_bitrate = 500000;                  // Minimum bitrate 500Kbps
    int max_bitrate = 5000000;                 // Maximum bitrate 5Mbps

    // Debug options
    int gst_debug_level = 2;                   // GStreamer debug level (0-5)
};

#ifdef __cplusplus
}
#endif

#endif // RTSP_SERVER_CONFIG_H__