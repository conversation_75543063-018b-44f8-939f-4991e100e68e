
#ifndef CLOUD_STREAMER_H
#define CLOUD_STREAMER_H

#include "../include/common.h"
#include "../include/config/cloud_streamer_config.h"
#include "../include/transport/video_transport_interface.h"
#include <atomic>
#include <thread>
#include <gst/sdp/sdp.h>
#include <gst/webrtc/webrtc.h>


// WebRTC信令处理
class WebRTCSignaling {
private:
    std::string signaling_server_;
    std::string room_id_;
    bool connected_ = false;

public:
    bool init(const std::string& server, const std::string& room);
    bool send_offer(const std::string& sdp);
    bool send_answer(const std::string& sdp);
    bool send_ice_candidate(const std::string& candidate);
    void cleanup();

    // 回调函数
    std::function<void(const std::string&)> on_answer_received;
    std::function<void(const std::string&)> on_ice_candidate_received;
};

class CloudStreamer {
private:
    CloudStreamerConfig config_;
    std::atomic<bool> stop_requested_{false};
    std::atomic<bool> running_{false};
    std::thread streamer_thread_;

    // Transport接口
    video_transport::IVideoSubscriber* video_subscriber_;

    // GStreamer相关
    GstElement* pipeline_ = nullptr;
    GstElement* appsrc_ = nullptr;
    GMainLoop* main_loop_ = nullptr;
    std::thread gst_thread_;

    // WebRTC相关
    std::unique_ptr<WebRTCSignaling> webrtc_signaling_;
    GstElement* webrtcbin_ = nullptr;

    // 统计信息
    std::atomic<uint64_t> frames_sent_{0};
    std::atomic<uint64_t> frames_dropped_{0};
    std::atomic<uint64_t> bytes_sent_{0};

    // 性能监控
    CPUMonitor cpu_monitor_;

public:
    CloudStreamer(const CloudStreamerConfig& config): config_(config) {};
    ~CloudStreamer() { stop(); }

    bool init(video_transport::IVideoSubscriber* subscriber) {
        if (running_.load()) {
            LOG_W("Cloud streamer already running");
            return true;
        }

        // 初始化GStreamer
        if (!gst_is_initialized()) {
            gst_init(nullptr, nullptr);
        }

        // 设置视频订阅者
        video_subscriber_ = subscriber;
        if (!video_subscriber_) {
            LOG_E("Failed to initialize video subscriber");
            return false;
        }

        // 初始化流管道
        if (config_.type == CloudStreamerConfig::WEBRTC) {
            if (!init_webrtc_pipeline()) {
                LOG_E("Failed to initialize WebRTC pipeline");
                return false;
            }
        } else {
            if (!init_rtmp_pipeline()) {
                LOG_E("Failed to initialize RTMP pipeline");
                return false;
            }
        }

        LOG_I("Cloud streamer initialized for %s streaming to %s",
              (config_.type == CloudStreamerConfig::WEBRTC) ? "WebRTC" : "RTMP",
              config_.url.c_str());
        return true;
    }

    void start() {
        if (running_.load()) {
            LOG_W("Cloud streamer already running");
            return;
        }

        stop_requested_.store(false);

        // 启动GStreamer主循环
        main_loop_ = g_main_loop_new(nullptr, FALSE);
        gst_thread_ = std::thread([this] {
            g_main_loop_run(main_loop_);
        });

        // 启动流处理线程
        streamer_thread_ = std::thread(&CloudStreamer::run, this);
        running_.store(true);

        // 启动管道
        if (pipeline_) {
            gst_element_set_state(pipeline_, GST_STATE_PLAYING);
        }

        LOG_I("Cloud streamer started");
    }

    void stop() {
        if (!running_.load()) {
            return;
        }

        stop_requested_.store(true);

        // 停止管道
        if (pipeline_) {
            gst_element_set_state(pipeline_, GST_STATE_NULL);
        }

        // 停止主循环
        if (main_loop_) {
            g_main_loop_quit(main_loop_);
        }

        // 等待线程结束
        if (streamer_thread_.joinable()) {
            streamer_thread_.join();
        }

        if (gst_thread_.joinable()) {
            gst_thread_.join();
        }

        running_.store(false);
        cleanup();

        LOG_I("Cloud streamer stopped");
    }

    void run() {
        LOG_I("Cloud streamer thread started");

        while (!stop_requested_.load()) {
            try {
                video_transport::IVideoSubscriber::SubscriberData input_frame;
                // 从队列中获取帧 100ms超时
                if (!video_subscriber_->receive_frame_buffer(input_frame, 100)) {
                    continue;
                }

                if (push_frame(input_frame)) {
                    frames_sent_.fetch_add(1);
                    bytes_sent_.fetch_add(input_frame.meta.data_size);
                } else {
                    frames_dropped_.fetch_add(1);
                }
            } catch (const std::exception& e) {
                LOG_E("Cloud streamer loop exception: %s", e.what());
                frames_dropped_.fetch_add(1);
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        LOG_I("Cloud streamer thread stopped");
    }

    // 获取统计信息
    struct Stats {
        uint64_t frames_sent;
        uint64_t frames_dropped;
        uint64_t bytes_sent;
        float bitrate_kbps;
        float fps;
        float cpu_usage;
    };

    void get_stats(Stats& stats) {
        static auto last_time = std::chrono::steady_clock::now();
        static uint64_t last_bytes = 0;
        static uint64_t last_frames = 0;

        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_time).count();

        uint64_t current_bytes = bytes_sent_.load();
        uint64_t current_frames = frames_sent_.load();

        float bitrate_kbps = 0.0f;
        float fps = 0.0f;

        if (elapsed > 0) {
            bitrate_kbps = (float)(current_bytes - last_bytes) * 8 / (elapsed * 1000);
            fps = (float)(current_frames - last_frames) / elapsed;
        }

        last_time = now;
        last_bytes = current_bytes;
        last_frames = current_frames;

        stats.frames_sent = current_frames;
        stats.frames_dropped = frames_dropped_.load();
        stats.bytes_sent = current_bytes;
        stats.bitrate_kbps = bitrate_kbps;
        stats.fps = fps;
        stats.cpu_usage = cpu_monitor_.get_usage();
    }

private:
    bool push_frame(const video_transport::IVideoSubscriber::SubscriberData& frame) {
        if (!appsrc_ || !frame.meta.is_valid) {
            return false;
        }

        GstBuffer* gst_buf = nullptr;

        // 使用软件缓冲区
        if (frame.data_ptr == nullptr) {
            LOG_W("Frame data is empty");
            return false;
        }

        gst_buf = gst_buffer_new_allocate(nullptr, frame.meta.data_size, nullptr);
        if (!gst_buf) {
            LOG_E("Failed to allocate GStreamer buffer");
            return false;
        }

        GstMapInfo map;
        if (!gst_buffer_map(gst_buf, &map, GST_MAP_WRITE)) {
            LOG_E("Failed to map GStreamer buffer");
            gst_buffer_unref(gst_buf);
            return false;
        }

        memcpy(map.data, frame.data_ptr, frame.meta.data_size);
        gst_buffer_unmap(gst_buf, &map);

        // 设置时间戳
        GST_BUFFER_PTS(gst_buf) = frame.meta.timestamp * 1000; // us -> ns
        GST_BUFFER_DTS(gst_buf) = GST_BUFFER_PTS(gst_buf);

        // 设置关键帧标志
        if (frame.meta.is_keyframe) {
            GST_BUFFER_FLAG_UNSET(gst_buf, GST_BUFFER_FLAG_DELTA_UNIT);
        } else {
            GST_BUFFER_FLAG_SET(gst_buf, GST_BUFFER_FLAG_DELTA_UNIT);
        }

        // 推送到GStreamer
        GstFlowReturn ret = gst_app_src_push_buffer(GST_APP_SRC(appsrc_), gst_buf);
        if (ret != GST_FLOW_OK) {
            LOG_E("Failed to push buffer to appsrc: %d", ret);
            return false;
        }

        return true;
    }
    
    bool init_webrtc_pipeline() {
        // 创建WebRTC管道
        std::string pipeline_desc =
            "appsrc name=source ! queue ! h264parse ! rtph264pay ! "
            "webrtcbin bundle-policy=max-bundle name=webrtcbin "
            "stun-server=stun://stun.l.google.com:19302";

        GError* error = nullptr;
        pipeline_ = gst_parse_launch(pipeline_desc.c_str(), &error);

        if (!pipeline_ || error) {
            LOG_E("Failed to create WebRTC pipeline: %s",
                  error ? error->message : "unknown error");
            if (error) g_error_free(error);
            return false;
        }

        // 获取appsrc元素
        appsrc_ = gst_bin_get_by_name(GST_BIN(pipeline_), "source");
        if (!appsrc_) {
            LOG_E("Failed to get appsrc element");
            return false;
        }

        // 配置appsrc
        g_object_set(appsrc_,
            "stream-type", GST_APP_STREAM_TYPE_STREAM,
            "format", GST_FORMAT_TIME,
            "is-live", TRUE,
            nullptr);

        // 配置caps
        GstCaps* caps = gst_caps_new_simple("video/x-h264",
            "width", G_TYPE_INT, config_.width,
            "height", G_TYPE_INT, config_.height,
            "framerate", GST_TYPE_FRACTION, config_.fps, 1,
            "stream-format", G_TYPE_STRING, "byte-stream",
            "alignment", G_TYPE_STRING, "au",
            nullptr);
        g_object_set(appsrc_, "caps", caps, nullptr);
        gst_caps_unref(caps);

        // 获取webrtcbin元素
        webrtcbin_ = gst_bin_get_by_name(GST_BIN(pipeline_), "webrtcbin");
        if (!webrtcbin_) {
            LOG_E("Failed to get webrtcbin element");
            return false;
        }

        // 设置WebRTC信号处理
        g_signal_connect(webrtcbin_, "on-negotiation-needed",
                        G_CALLBACK(on_negotiation_needed), this);
        g_signal_connect(webrtcbin_, "on-ice-candidate",
                        G_CALLBACK(on_ice_candidate), this);
        g_signal_connect(webrtcbin_, "on-offer-created",
                        G_CALLBACK(on_offer_created), this);

        // 初始化WebRTC信令
        webrtc_signaling_ = std::make_unique<WebRTCSignaling>();
        if (!webrtc_signaling_->init(config_.url, "room1")) {
            LOG_W("WebRTC signaling initialization failed");
        }

        LOG_I("WebRTC pipeline initialized");
        return true;
    }
    
    bool init_rtmp_pipeline() {
        // 创建RTMP管道
        std::string pipeline_desc =
            "appsrc name=source ! queue ! h264parse ! flvmux ! "
            "rtmpsink location=" + config_.url + " live=true";

        GError* error = nullptr;
        pipeline_ = gst_parse_launch(pipeline_desc.c_str(), &error);

        if (!pipeline_ || error) {
            LOG_E("Failed to create RTMP pipeline: %s",
                  error ? error->message : "unknown error");
            if (error) g_error_free(error);
            return false;
        }

        // 获取appsrc元素
        appsrc_ = gst_bin_get_by_name(GST_BIN(pipeline_), "source");
        if (!appsrc_) {
            LOG_E("Failed to get appsrc element");
            return false;
        }

        // 配置appsrc
        g_object_set(appsrc_,
            "stream-type", GST_APP_STREAM_TYPE_STREAM,
            "format", GST_FORMAT_TIME,
            "is-live", TRUE,
            nullptr);

        // 配置caps
        GstCaps* caps = gst_caps_new_simple("video/x-h264",
            "width", G_TYPE_INT, config_.width,
            "height", G_TYPE_INT, config_.height,
            "framerate", GST_TYPE_FRACTION, config_.fps, 1,
            "stream-format", G_TYPE_STRING, "byte-stream",
            "alignment", G_TYPE_STRING, "au",
            nullptr);
        g_object_set(appsrc_, "caps", caps, nullptr);
        gst_caps_unref(caps);

        LOG_I("RTMP pipeline initialized for %s", config_.url.c_str());
        return true;
    }

    void cleanup() {
        // 清理WebRTC信令
        webrtc_signaling_.reset();

        // 清理GStreamer资源
        if (pipeline_) {
            gst_object_unref(pipeline_);
            pipeline_ = nullptr;
        }

        if (main_loop_) {
            g_main_loop_unref(main_loop_);
            main_loop_ = nullptr;
        }

        appsrc_ = nullptr;
        webrtcbin_ = nullptr;
    }

    // WebRTC回调函数
    static void on_negotiation_needed(GstElement* webrtc, gpointer user_data) {
        CloudStreamer* self = static_cast<CloudStreamer*>(user_data);
        LOG_I("WebRTC negotiation needed");

        // 创建offer
        GstPromise* promise = gst_promise_new_with_change_func(on_offer_created, user_data, nullptr);
        g_signal_emit_by_name(webrtc, "create-offer", nullptr, promise);
    }

    static void on_offer_created(GstPromise* promise, gpointer user_data) {
        CloudStreamer* self = static_cast<CloudStreamer*>(user_data);

        const GstStructure* reply = gst_promise_get_reply(promise);
        GstWebRTCSessionDescription* offer = nullptr;
        gst_structure_get(reply, "offer", GST_TYPE_WEBRTC_SESSION_DESCRIPTION, &offer, nullptr);

        // 设置本地描述
        GstPromise* local_promise = gst_promise_new();
        g_signal_emit_by_name(self->webrtcbin_, "set-local-description", offer, local_promise);
        gst_promise_unref(local_promise);

        // 发送offer到信令服务器
        if (self->webrtc_signaling_) {
            gchar* sdp_text = gst_sdp_message_as_text(offer->sdp);
            self->webrtc_signaling_->send_offer(sdp_text);
            g_free(sdp_text);
        }

        gst_webrtc_session_description_free(offer);
        gst_promise_unref(promise);
    }

    static void on_ice_candidate(GstElement* webrtc, guint mline_index,
                                gchar* candidate, gpointer user_data) {
        CloudStreamer* self = static_cast<CloudStreamer*>(user_data);

        LOG_D("ICE candidate: %s", candidate);

        if (self->webrtc_signaling_) {
            self->webrtc_signaling_->send_ice_candidate(candidate);
        }
    }
};

// WebRTC信令实现（简化版）
bool WebRTCSignaling::init(const std::string& server, const std::string& room) {
    signaling_server_ = server;
    room_id_ = room;

    // 这里应该实现WebSocket连接到信令服务器
    // 为了简化，我们只是记录日志
    LOG_I("WebRTC signaling initialized (simplified implementation)");
    LOG_I("Server: %s, Room: %s", server.c_str(), room.c_str());

    connected_ = true;
    return true;
}

bool WebRTCSignaling::send_offer(const std::string& sdp) {
    if (!connected_) return false;

    LOG_I("Sending WebRTC offer (simplified)");
    LOG_D("SDP: %s", sdp.c_str());

    // 实际实现应该通过WebSocket发送到信令服务器
    return true;
}

bool WebRTCSignaling::send_answer(const std::string& sdp) {
    if (!connected_) return false;

    LOG_I("Sending WebRTC answer (simplified)");
    return true;
}

bool WebRTCSignaling::send_ice_candidate(const std::string& candidate) {
    if (!connected_) return false;

    LOG_D("Sending ICE candidate: %s", candidate.c_str());
    return true;
}

void WebRTCSignaling::cleanup() {
    connected_ = false;
    LOG_I("WebRTC signaling cleanup");
}

#endif // CLOUD_STREAMER_H
