#ifndef SHARED_BUFFER_PUBLISHER_H
#define SHARED_BUFFER_PUBLISHER_H

#include <liburing.h>
#include <liburing/io_uring.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <atomic>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <thread>
#include <iostream>
#include <mutex>
#include <condition_variable>
#include <cstring>
#include <chrono>
#include <queue>
#include <functional>
#include <sstream>
#include "shared_buffer_manager.h"
#include "video_transport_interface.h"
#include "../common.h"

namespace video_transport {
// 前向声明
class SharedBufferPublisher;
// 生产者服务器 - 实现 IVideoPublisher 接口
class SharedBufferPublisher : public IVideoPublisher {
public:
    enum class ConnectionState { ACCEPTING, CONNECTED, CLOSING };

    struct Connection {
        int fd;
        ConnectionState state;
        std::unordered_map<uint64_t, int> buffer_counts; // <ID, REFERENCE_COUNT>
    };

    struct BufferInfo {
        uint64_t buffer_id;
        int fd;
        size_t size;
        BufferInfo() : buffer_id(0), fd(-1), size(0) {}
        BufferInfo(uint64_t b, int f, size_t s) : buffer_id(b), fd(f), size(s) {}
    };
    
    enum class OperationType { ACCEPT, RECV, SEND, CLOSE };
    
    struct OperationData {
        OperationType type;
        int fd;
        Message* msg;
        OperationData(OperationType t, int f, Message* m) : type(t), fd(f), msg(m) {}
    };

    // 默认构造函数，用于初始化发布者对象。
    SharedBufferPublisher(const TransportConfig& config)
    : IVideoPublisher(config), socket_path_(config.topic_name), server_fd_(-1), running_(false), has_active_subscribers_(false), initialized_(false),
        buffer_manager_(config.type == TransportType::DMA ? SharedBufferManager::BufferType::DMA : SharedBufferManager::BufferType::SHMEM, config.buffer_size, config.ring_buffer_size),
        connections_(), active_buffers_() {}  // 初始化发布者对象。
    // 默认析构函数，用于清理发布者对象。
    ~SharedBufferPublisher() override { cleanup(); }
    
    // IVideoPublisher接口实现
    bool initialize() override;
    void cleanup() override;
    BufferResult acquire_buffer(PublisherData& data) override;
    BufferResult publish_buffer(PublisherData& data) override;
    int get_dma_fd(const PublisherData& data) override;
    bool has_subscribers() const override;
    TransportStats get_stats() const override;
    void reset_stats() override;
    std::string get_status() const override;
    bool supports_v4l2_zero_copy() const override { return true; }

private:
    void setup_server_socket();
    void setup_io_uring();
    void process_events();
    void broadcast_frame(FrameMetadata & meta, uint64_t buffer_id);
    void submit_accept();
    void submit_send_frame(int client_fd, Message * msg);
    void submit_send_frame(int client_fd, FrameMetadata & meta, uint64_t buffer_id);
    void submit_send_buffer(int client_fd, uint64_t buffer_id, int buffer_fd, size_t buffer_size);  // 发送缓冲区ID和缓冲区描述符。
    void submit_recv(int client_fd);
    void handle_accept(struct io_uring_cqe* cqe, OperationData* data);
    void handle_recv(struct io_uring_cqe* cqe, OperationData* data);
    void handle_send(struct io_uring_cqe* cqe, OperationData* data);
    void handle_close(struct io_uring_cqe* cqe, OperationData* data);
    void close_connection(int client_fd);
    void update_stats_sent(size_t bytes, bool success);

    std::string socket_path_;  // 套接字路径，用于存储套接字的路径名或文件名，用于与订阅者进行通信。套接字路径的长度应小于或等于sizeof(sockaddr_un::sun_path) - 1，以便在套接字路径中存储完整的路径名。
    int server_fd_;  // 服务器套接字描述符，用于与订阅者进行通信。
    std::atomic<bool> running_;  // 运行标志，用于控制事件循环的运行/停止
    std::atomic<bool> has_active_subscribers_;  // 指示是否有活动订阅者
    std::atomic<bool> initialized_;  // 初始化标志，用于指示发布者是否已初始化
    io_uring ring_;  // io_uring 结构体，用于管理IO操作。io_uring 结构体包含了IO操作的相关信息，如IO操作的类型、文件描述符、缓冲区地址等。
    std::thread event_thread_;  // 事件线程，用于处理IO操作。
    
    // 核心组件
    SharedBufferManager buffer_manager_;  // 共享缓冲区管理器，用于管理DMA和共享内存缓冲区
    std::unordered_map<int, Connection> connections_;  // 连接列表，用于跟踪与订阅者的连接
    std::unordered_map<uint64_t, BufferInfo> active_buffers_;  // 活动缓冲区列表，用于跟踪当前正在使用的缓冲区
    
    mutable std::mutex stats_mutex_;  // 用于保护统计信息的互斥锁，用于确保统计信息的同步访问。
    TransportStats stats_;  // 统计信息，用于跟踪发布者的性能指标，如发送的消息数量、字节数等。
};

// ========================================
// SharedBufferPublisher 实现
// ========================================

inline bool SharedBufferPublisher::initialize() {
    if (initialized_.load()) {
        LOG_I("SharedBufferPublisher already initialized");
        return true;
    }
    
    if (config_.type != TransportType::DMA && config_.type != TransportType::SHMEM) {
        LOG_E("Invalid config type for SharedBuffer transport"); 
        return false;
    }
    
    try {
        setup_server_socket();
        setup_io_uring();
        submit_accept();
        running_.store(true);
        initialized_.store(true);
        event_thread_ = std::thread(&SharedBufferPublisher::process_events, this);

        LOG_I("SharedBufferPublisher type: %s initialized successfully on socket : %s", 
              config_.type == TransportType::DMA ? "DMA" : "SHMEM", 
              config_.topic_name.c_str());
        return true;
    } catch (const std::exception& e) {
        LOG_E("Failed to initialize SharedBufferPublisher: %s", e.what());
        cleanup();
        return false;
    }
}

inline void SharedBufferPublisher::cleanup() {
    if (!initialized_.load()) return;

    running_.store(false);
    if (event_thread_.joinable()) {
        struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
        if (sqe) {
            io_uring_prep_nop(sqe);
            io_uring_submit(&ring_);
            event_thread_.join();
        }
    }
    for (auto& [fd, conn] : connections_) close(fd);  // 关闭所有活动连接的套接字描述符。
    if (server_fd_ >= 0) {
        close(server_fd_);
        server_fd_ = -1;
    }
    if (!socket_path_.empty()) {
        unlink(socket_path_.c_str());
    }
    io_uring_queue_exit(&ring_);
    initialized_.store(false);  // 设置初始化标志为false，表示发布者已被清理。
    
    LOG_I("SharedBufferPublisher type: %s cleaned up successfully", config_.type == TransportType::DMA ? "DMA" : "SHMEM");  // 日志记录清理成功。
}

inline BufferResult SharedBufferPublisher::acquire_buffer(PublisherData& data) {    
    try {
        auto* slot = buffer_manager_.acquire_buffer();
        if (!slot) {
            throw std::runtime_error("Failed to acquire buffer");
        }
        
        // 设置PublisherData
        data.buffer_id = slot->buffer_id;
        data.data_ptr = slot->addr;
        data.transport_type = (slot->type == SharedBufferManager::BufferType::DMA) ? TransportType::DMA : TransportType::SHMEM;
        
        // 如果该缓冲区尚未发布，则将其添加到活动缓冲区列表中。如果该缓冲区已发布，则不应将其添加到活动缓冲区列表中，因为它已在发布时添加到活动缓冲区列表中。
        if (active_buffers_.find(data.buffer_id) == active_buffers_.end()) {
            LOG_I("Add new buffer to active_buffers_, buffer id: %d, fd: %d", data.buffer_id, slot->fd);
            active_buffers_[data.buffer_id] = {slot->buffer_id, slot->fd, slot->size};
            // 向所有已连接的客户端发送此缓冲区
            for (const auto& [fd, conn] : connections_) {
                submit_send_buffer(fd, slot->buffer_id, slot->fd, slot->size);
            }
        }

        return BufferResult::SUCCESS;
    } catch (const std::exception& e) {
        LOG_E("Failed to acquire buffer: %s", e.what());
        return BufferResult::TRANSPORT_ERROR;
    }
}

inline int SharedBufferPublisher::get_dma_fd(const PublisherData& data) {
    auto* slot = buffer_manager_.find_buffer(data.buffer_id);
    if (!slot) {
        return -1;
    }
    return slot->fd;
}

inline BufferResult SharedBufferPublisher::publish_buffer(PublisherData& data) {
    try {
        // 发布到buffer manager，并更新数据指针和大小信息（如果需要）
        auto* slot = buffer_manager_.find_buffer(data.buffer_id);
        if (!slot) {
            throw std::runtime_error("Invalid buffer ID");
        }
        
        if (slot->type == SharedBufferManager::BufferType::SHMEM) {
            // 共享内存，需要拷贝数据到缓冲区中，并更新数据指针和大小信息, data.data_ptr 指向的是v4l2缓冲区的地址，slot->addr 指向的是共享内存缓冲区的地址。
            // LOG_I("Copy data to shared memory buffer, buffer id: %d, size: %d, v4l2 addr: %p, share addr: %p", data.buffer_id, data.meta.data_size, data.data_ptr, slot->addr);
            memcpy(slot->addr, data.data_ptr, data.meta.data_size);  // 将数据从data.data_ptr复制到slot->addr，即共享内存缓冲区的地址。
        }

        buffer_manager_.publish_buffer(slot);        
        // 分发给所有连接的消费者
        broadcast_frame(data.meta, data.buffer_id);
        update_stats_sent(data.meta.data_size, true);
        return BufferResult::SUCCESS;
    } catch (const std::exception& e) {
        LOG_E("Failed to publish buffer: %s", e.what());
        update_stats_sent(0, false);
        return BufferResult::TRANSPORT_ERROR;
    }
}

inline void SharedBufferPublisher::broadcast_frame(FrameMetadata & meta, uint64_t buffer_id) {
    // Send frame notification to all connected consumers
    for (const auto& [fd, conn] : connections_) {
        if (conn.state == ConnectionState::CONNECTED) {
            submit_send_frame(fd, meta, buffer_id);
        }
    }
}

inline void SharedBufferPublisher::submit_send_frame(int client_fd, Message * msg) {
    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;
    
    auto* data = new OperationData(OperationType::SEND, client_fd, msg);
    io_uring_prep_sendmsg(sqe, client_fd, &data->msg->hdr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void SharedBufferPublisher::submit_send_frame(int client_fd, FrameMetadata & meta, uint64_t buffer_id) {
    auto * msg = new Message(meta, buffer_id);
    submit_send_frame(client_fd, msg);
}

inline void SharedBufferPublisher::submit_send_buffer(int client_fd, uint64_t buffer_id, int buffer_fd, size_t buffer_size) {
    FrameMetadata meta = {};
    meta.data_size = buffer_size;
    auto * msg = new Message(meta, buffer_id, buffer_fd);
    submit_send_frame(client_fd, msg);
}


inline void SharedBufferPublisher::handle_send(struct io_uring_cqe* cqe, OperationData* data) {
    int bytes_sent = cqe->res;
    int client_fd = data->fd;
    if (bytes_sent < 0) {
        // Send failed, close connection
        close_connection(client_fd);
        update_stats_sent(0, false);
    }
    delete data->msg;
    delete data;
}

inline bool SharedBufferPublisher::has_subscribers() const {
    return has_active_subscribers_.load();
}

inline TransportStats SharedBufferPublisher::get_stats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return stats_;
}

inline void SharedBufferPublisher::reset_stats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    stats_ = TransportStats();
}

inline std::string SharedBufferPublisher::get_status() const {
    std::ostringstream oss;
    oss << "SharedBufferPublisher: " 
        << (initialized_.load() ? "initialized" : "not initialized")
        << ", subscribers: " << (has_active_subscribers_.load() ? "yes" : "no")
        << ", socket: " << socket_path_;
    return oss.str();
}

// Private method implementations for SharedBufferPublisher
inline void SharedBufferPublisher::setup_server_socket() {
    server_fd_ = socket(AF_UNIX, SOCK_SEQPACKET, 0);
    if (server_fd_ < 0) {
        throw std::runtime_error("Failed to create server socket");
    }
    
    // Remove existing socket file
    unlink(socket_path_.c_str());
    
    struct sockaddr_un addr = {};
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, socket_path_.c_str(), sizeof(addr.sun_path)-1);
    
    if (bind(server_fd_, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        close(server_fd_);
        throw std::runtime_error("Failed to bind server socket");
    }
    
    if (listen(server_fd_, 10) < 0) {
        close(server_fd_);
        throw std::runtime_error("Failed to listen on server socket");
    }
}

inline void SharedBufferPublisher::setup_io_uring() {
    struct io_uring_params params = {};
    if (io_uring_queue_init_params(1024, &ring_, &params)) {
        throw std::runtime_error("Failed to initialize io_uring");
    }
}

inline void SharedBufferPublisher::submit_accept() {
    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;
    
    auto* data = new OperationData(OperationType::ACCEPT, server_fd_, nullptr);
    
    io_uring_prep_accept(sqe, server_fd_, nullptr, nullptr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void SharedBufferPublisher::handle_accept(struct io_uring_cqe* cqe, OperationData* data) {
    int client_fd = cqe->res;
    delete data;
    
    if (client_fd < 0) {
        if (running_.load()) {
            submit_accept();  // Continue accepting
        }
        return;
    }
    
    // Add new connection
    connections_[client_fd] = {client_fd, ConnectionState::CONNECTED, {}};
    has_active_subscribers_.store(true);
    LOG_I("New client ID: %d connected, total clients: %zu", client_fd, connections_.size());
    
    // Send all active buffers to this client
    for (auto& [buffer_id, buffer_info] : active_buffers_) {
        submit_send_buffer(client_fd, buffer_info.buffer_id, buffer_info.fd, buffer_info.size);
    }
    
    // Continue accepting new connections
    if (running_.load()) {
        submit_accept();
    }
}

inline void SharedBufferPublisher::submit_recv(int client_fd) {
    struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
    if (!sqe) return;
    
    auto* data = new OperationData(OperationType::RECV, client_fd, new Message());
    
    io_uring_prep_recvmsg(sqe, client_fd, &data->msg->hdr, 0);
    io_uring_sqe_set_data(sqe, data);
    io_uring_submit(&ring_);
}

inline void SharedBufferPublisher::handle_recv(struct io_uring_cqe* cqe, OperationData* data) {
    int bytes_received = cqe->res;
    int client_fd = data->fd;
    
    if (bytes_received <= 0) {
        // Client disconnected or error
        delete data->msg;
        struct io_uring_sqe* sqe = io_uring_get_sqe(&ring_);
        if (!sqe) {
            delete data;
            return;
        }
        data->msg = nullptr;
        data->type = OperationType::CLOSE;        
        io_uring_sqe_set_data(sqe, data);
        io_uring_submit(&ring_);
        return;
    }
    
    auto* msg = data->msg;

    // Handle release message from consumer
    struct cmsghdr* cmsg = CMSG_FIRSTHDR(&msg->hdr);
    if (cmsg && cmsg->cmsg_type == SCM_RIGHTS) {
        // int transfer_fd = *((int*)CMSG_DATA(cmsg));
        uint64_t buffer_id = msg->transport_data.buffer_id;
        auto* slot = buffer_manager_.find_buffer(buffer_id);
        if (slot) {
            // Decrease reference count for this buffer
            auto& conn = connections_[client_fd];
            if (conn.buffer_counts.find(buffer_id) != conn.buffer_counts.end()) {
                conn.buffer_counts[buffer_id]--;
                buffer_manager_.release_from_reading(slot, 1);
                LOG_I("Released buffer, buffer id: %d, from client %d", buffer_id, client_fd);
            }
        }
    }

    // Continue receiving from this client
    submit_recv(client_fd);
    delete data->msg;
    delete data;
}

inline void SharedBufferPublisher::handle_close(struct io_uring_cqe* cqe, OperationData* data) {
    (void) cqe;
    if (data) {
        close_connection(data->fd);
        delete data;
    }
}

inline void SharedBufferPublisher::close_connection(int client_fd) {
    auto it = connections_.find(client_fd);
    if (it != connections_.end()) {
        // Clean up all buffer references for this consumer
        for (auto& [buffer_id, count] : it->second.buffer_counts) {
            auto* slot = buffer_manager_.find_buffer(buffer_id);
            if (slot) {
                buffer_manager_.release_from_reading(slot, count);
            }
        }
        
        connections_.erase(it);
        close(client_fd);  // Close the client socket.
        
        // Update subscriber status
        has_active_subscribers_.store(!connections_.empty());
        LOG_I("Client disconnected, total clients: %zu", connections_.size());  // 日志记录客户端断开连接。
    }
}

inline void SharedBufferPublisher::process_events() {    
    while (running_.load()) {
        struct io_uring_cqe* cqe;
        
        int ret = io_uring_wait_cqe(&ring_, &cqe);
        
        if (ret < 0) {
            if (errno == EINTR) continue;
            break;
        }
        
        auto* data = static_cast<OperationData*>(io_uring_cqe_get_data(cqe));
        if (!data) {
            io_uring_cqe_seen(&ring_, cqe);
            continue;
        }
        
        switch (data->type) {
            case OperationType::ACCEPT:
                handle_accept(cqe, data);
                break;
            case OperationType::RECV:
                handle_recv(cqe, data);
                break;
            case OperationType::SEND:
                handle_send(cqe, data);
                break;
            case OperationType::CLOSE:
                handle_close(cqe, data);
                break;
        }
        
        io_uring_cqe_seen(&ring_, cqe);
    }
}

inline void SharedBufferPublisher::update_stats_sent(size_t bytes, bool success) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    if (success) {
        stats_.frames_sent.fetch_add(1);
        stats_.bytes_sent.fetch_add(bytes);
    } else {
        stats_.failed_operations.fetch_add(1);
    }
    stats_.last_update_time.store(std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
}

} // namespace video_transport

#endif // SHARED_BUFFER_PUBLISHER_H