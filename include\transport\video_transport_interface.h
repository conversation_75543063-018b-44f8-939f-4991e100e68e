#ifndef VIDEO_TRANSPORT_INTERFACE_H
#define VIDEO_TRANSPORT_INTERFACE_H

#include <memory>
#include <functional>
#include <atomic>
#include <string>
#include <vector>
#include <mutex>
#include <cstring>
#include <sys/socket.h>
#include <sys/un.h>

// Forward declarations
namespace V4L2Capture {
    struct V4L2Frame;
    struct V4L2Buffer;
}

namespace video_transport {

// Forward declarations for structures that cause circular dependencies
struct ReleaseMessage;
struct FrameMetadata;

// 传输类型
enum class TransportType {
    FASTDDS,        // FastDDS - 完整帧数据复制传输
    DMA,            // DMA缓冲区 - io_uring + Unix socket DMA传输
    SHMEM           // 共享内存 - io_uring + Unix socket SHMEM传输
};

// 传输配置
struct TransportConfig {
    TransportType type;
    std::string topic_name;
    
    // FastDDS specific
    int domain_id = 0;
    int max_samples = 4;
    
    // DMA specific
    size_t buffer_size = 0;      // DMA缓冲区大小
    size_t ring_buffer_size = 4; // 环形缓冲区大小
    
    int timeout_ms = 1000;
};

// 缓冲区处理结果
enum class BufferResult {
    SUCCESS,
    BUFFER_NOT_AVAILABLE,
    TRANSPORT_ERROR,
    TIMEOUT,
    INVALID_DATA
};

// Frame metadata for business logic
struct FrameMetadata {
    uint64_t frame_id;      // Frame ID
    uint64_t timestamp;     // Frame timestamp (microseconds)
    uint32_t width;         // Frame width
    uint32_t height;        // Frame height
    uint32_t format;        // Pixel format
    uint32_t data_size;     // Valid data size
    bool is_valid;          // Whether the metadata is valid
    bool is_keyframe;       // Whether the frame is a keyframe
}  __attribute__((packed));


//跨进程传输的数据结构，需要字节对齐，方便传输
struct TransportData {
    uint64_t buffer_id;
    FrameMetadata meta;
    TransportData() : buffer_id(0) { memset(&meta, 0, sizeof(meta)); }
    TransportData(const FrameMetadata& m, uint64_t b) :  buffer_id(b), meta(m) {}
}  __attribute__((packed));

// 发送消息结构体
struct Message {
    msghdr  hdr;
    iovec   iov[1];
    char    ctrl_buf[CMSG_SPACE(sizeof(int))];
    TransportData transport_data;

    /* 构造 0： 接收 fd 和 元数据 */
    explicit Message() {
        memset(&hdr, 0, sizeof(hdr));
        iov[0] = {&transport_data, sizeof(transport_data)};
        hdr.msg_iov    = iov;
        hdr.msg_iovlen = 1;

        hdr.msg_control    = ctrl_buf;
        hdr.msg_controllen = sizeof(ctrl_buf);

        struct cmsghdr* cmsg = CMSG_FIRSTHDR(&hdr);
        cmsg->cmsg_level = SOL_SOCKET;
        cmsg->cmsg_type  = SCM_RIGHTS;
        cmsg->cmsg_len   = CMSG_LEN(sizeof(int));
    }

    /* 构造 1：发送 fd */
    explicit Message(int fd) {
        memset(&hdr, 0, sizeof(hdr));
        // 不设置 iov，或长度置 0
        hdr.msg_iov    = nullptr;
        hdr.msg_iovlen = 0;

        hdr.msg_control    = ctrl_buf;
        hdr.msg_controllen = sizeof(ctrl_buf);

        struct cmsghdr* cmsg = CMSG_FIRSTHDR(&hdr);
        cmsg->cmsg_level = SOL_SOCKET;
        cmsg->cmsg_type  = SCM_RIGHTS;
        cmsg->cmsg_len   = CMSG_LEN(sizeof(int));
        *reinterpret_cast<int*>(CMSG_DATA(cmsg)) = fd;
    }

    /* 构造 2：仅发送元数据，不带 fd */
    explicit Message(const FrameMetadata& meta, uint64_t buffer_id)
        : transport_data(meta, buffer_id) {
        memset(&hdr, 0, sizeof(hdr));
        iov[0] = {&transport_data, sizeof(transport_data)};
        hdr.msg_iov    = iov;
        hdr.msg_iovlen = 1;

        hdr.msg_control    = nullptr;   // 关键：不携带控制信息
        hdr.msg_controllen = 0;
    }

    /* 构造 3：发送元数据 + fd */
    explicit Message(const FrameMetadata& meta, uint64_t buffer_id, int fd)
        : transport_data(meta, buffer_id) {
        
        memset(&hdr, 0, sizeof(hdr));
        iov[0] = {&transport_data, sizeof(transport_data)};
        hdr.msg_iov    = iov;
        hdr.msg_iovlen = 1;

        hdr.msg_control    = ctrl_buf;
        hdr.msg_controllen = sizeof(ctrl_buf);

        struct cmsghdr* cmsg = CMSG_FIRSTHDR(&hdr);
        cmsg->cmsg_level = SOL_SOCKET;
        cmsg->cmsg_type  = SCM_RIGHTS;
        cmsg->cmsg_len   = CMSG_LEN(sizeof(int));
        *reinterpret_cast<int*>(CMSG_DATA(cmsg)) = fd;
    }
};

// 传输统计信息
struct TransportStats {
    std::atomic<uint64_t> frames_sent{0};
    std::atomic<uint64_t> frames_received{0};
    std::atomic<uint64_t> bytes_sent{0};
    std::atomic<uint64_t> bytes_received{0};
    std::atomic<uint64_t> dropped_frames{0};
    std::atomic<uint64_t> failed_operations{0};
    std::atomic<uint64_t> last_update_time{0};
    
    // 添加默认构造函数
    TransportStats() = default;
    
    // 添加拷贝构造函数
    TransportStats(const TransportStats& other) 
        : frames_sent(other.frames_sent.load()),
          frames_received(other.frames_received.load()),
          bytes_sent(other.bytes_sent.load()),
          bytes_received(other.bytes_received.load()),
          dropped_frames(other.dropped_frames.load()),
          failed_operations(other.failed_operations.load()),
          last_update_time(other.last_update_time.load()) {}
          
    // 添加赋值操作符
    TransportStats& operator=(const TransportStats& other) {
        if (this != &other) {
            frames_sent.store(other.frames_sent.load());
            frames_received.store(other.frames_received.load());
            bytes_sent.store(other.bytes_sent.load());
            bytes_received.store(other.bytes_received.load());
            dropped_frames.store(other.dropped_frames.load());
            failed_operations.store(other.failed_operations.load());
            last_update_time.store(other.last_update_time.load());
        }
        return *this;
    }
};

// 视频帧发布者接口
class IVideoPublisher {
public:
    struct PublisherData {
        uint64_t buffer_id;
        FrameMetadata meta;
        void * data_ptr;
        TransportType transport_type;
        PublisherData() { memset(&meta, 0, sizeof(meta)); data_ptr = nullptr; }
    };

    IVideoPublisher(const TransportConfig& config) : config_(config) {}  // 默认构造函数，用于初始化发布者对象
    virtual ~IVideoPublisher() = default;
    
    // 初始化
    virtual bool initialize() = 0;
    virtual void cleanup() = 0;

    // 获取缓冲区用于填充帧数据
    virtual BufferResult acquire_buffer(PublisherData& data) = 0;
    
    // 发布已填充的缓冲区
    virtual BufferResult publish_buffer(PublisherData& data) = 0;
    
    // V4L2 DMABUF集成必需的DMA文件描述符访问
    virtual int get_dma_fd(const PublisherData& data) = 0;
    
    // 状态查询
    virtual bool has_subscribers() const = 0;
    virtual TransportStats get_stats() const = 0;
    virtual void reset_stats() = 0;
    virtual std::string get_status() const = 0;
    
    // **NEW: Check if this publisher supports V4L2 zero-copy integration**
    virtual bool supports_v4l2_zero_copy() const = 0;

    const TransportConfig& get_config() const { return config_; }

    TransportConfig config_;  // 发布者配置信息
};

// 视频帧订阅者接口
class IVideoSubscriber {
public:
    struct SubscriberData {
        uint64_t buffer_id;
        int fd;
        FrameMetadata meta;
        void* data_ptr;
        TransportType transport_type;
        SubscriberData() { 
            memset(&meta, 0, sizeof(meta)); 
            data_ptr = nullptr; 
            buffer_id = -1;
            fd = -1;
        }
    };
    
    IVideoSubscriber(const TransportConfig& config) : config_(config) {}  // 默认构造函数，用于初始化订阅者对象
    virtual ~IVideoSubscriber() = default;
    
    // 初始化
    virtual bool initialize() = 0;
    virtual void cleanup() = 0;
    
    // 接收并返回缓冲区 - 同步模式：receive -> process
    virtual BufferResult receive_frame_buffer(SubscriberData& data, int timeout_ms = 1000) = 0;
    
    // 回调接口- 异步模式: receive -> process
    virtual void set_buffer_callback(std::function<void(SubscriberData&)> callback) = 0;
    
    // 状态查询
    virtual bool is_connected() const = 0;
    virtual TransportStats get_stats() const = 0;
    virtual void reset_stats() = 0;
    virtual std::string get_status() const = 0;
    
    const TransportConfig& get_config() const { return config_; }  // 获取配置信息

    TransportConfig config_;  // 订阅者配置信息
};

// 工厂类
class VideoTransportFactory {
public:    
    static std::unique_ptr<IVideoPublisher> create_publisher(const TransportConfig& config);
    static std::unique_ptr<IVideoSubscriber> create_subscriber(const TransportConfig& config);
};

} // namespace video_transport

#endif // VIDEO_TRANSPORT_INTERFACE_H