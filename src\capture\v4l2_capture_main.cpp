#include "../include/common.h"
#include "../include/transport/video_transport_interface.h"
#include "../include/transport/fastdds_video_transport.h"
#include "../include/transport/shared_buffer_publisher.h"
#include "../include/config/v4l2_capture_config.h"
#include "../include/capture/v4l2_capture_interface.h"
#include "../include/capture/v4l2_utils.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <atomic>
#include <fstream>
#include <json/json.h>


using namespace video_transport;
using namespace V4L2Capture;

static std::atomic<bool> running(true);
static std::unique_ptr<IV4L2CaptureDevice> g_device;
static std::unique_ptr<IVideoPublisher> g_publisher;

// Configuration structure
struct AppConfig {
    std::string device_path = "/dev/video0";
    uint32_t width = 1920;
    uint32_t height = 1080;
    uint32_t pixel_format = V4L2_PIX_FMT_YUYV;
    uint32_t fps = 30;
    uint32_t buffer_count = 4;
    bool use_dma = true;
    TransportType transport_type = TransportType::DMA;
    std::string topic_name = "video_frames";
};

void signal_handler(int sig) {
    LOG_W("v4l2_capture_main: Received signal %d, stopping...", sig);
    running = false;
}

uint32_t string_to_pixelformat(const std::string& format_str) {
    if (format_str.length() != 4) {
        return 0;
    }
    return (format_str[0]) | (format_str[1] << 8) | (format_str[2] << 16) | (format_str[3] << 24);
}

bool load_config(const std::string& config_file, AppConfig& config) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        LOG_E("Failed to open config file: %s ", config_file.c_str());
        return false;
    }

    Json::Value root;
    Json::CharReaderBuilder builder;
    Json::parseFromStream(builder, file, &root, nullptr);

    // 注意：这里只在JSON文件中存在相应键时才更新配置
    // 保持默认配置作为最低优先级
    if (root.isMember("device")) {
        config.device_path = root["device"].asString();
    }
    if (root.isMember("width")) {
        config.width = root["width"].asUInt();
    }
    if (root.isMember("height")) {
        config.height = root["height"].asUInt();
    }
    if (root.isMember("pixel_format")) {
        std::string format_str = root["pixel_format"].asString();
        config.pixel_format = string_to_pixelformat(format_str);
    }
    if (root.isMember("fps")) {
        config.fps = root["fps"].asUInt();
    }
    if (root.isMember("buffer_count")) {
        config.buffer_count = root["buffer_count"].asUInt();
    }
    if (root.isMember("use_dma")) {
        config.use_dma = root["use_dma"].asBool();
    }
    if (root.isMember("topic_name")) {
        config.topic_name = root["topic_name"].asString();
    }
    if (root.isMember("transport_type")) {
        std::string transport_str = root["transport_type"].asString();
        if (transport_str == "FASTDDS") {
            config.transport_type = TransportType::FASTDDS;
        } else if (transport_str == "DMA") {
            config.transport_type = TransportType::DMA;
        } else if (transport_str == "SHMEM") {
            config.transport_type = TransportType::SHMEM;
        }
    }

    return true;
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -c, --config FILE     Configuration file (default: config/v4l2_capture_config.json)\n"
              << "  -d, --device PATH     V4L2 device path (default: /dev/video0)\n"
              << "  -w, --width WIDTH     Video width (default: 1920)\n"
              << "  -h, --height HEIGHT   Video height (default: 1080)\n"
              << "  -f, --fps FPS         Frame rate (default: 30)\n"
              << "  --format FORMAT       Pixel format (default: YUYV)\n"
              << "  --topic TOPIC         DDS topic name (default: video_frames)\n"
              << "  --buffer-count COUNT  Buffer count (default: 4)\n"
              << "  --no-dma              Disable DMA buffers\n"
              << "  --transport TYPE      Transport type (FASTDDS, DMA, SHMEM) (default: DMA)\n"
              << "  --help                Show this help message\n";
}

int main(int argc, char* argv[]) {
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 1. 默认配置（最低优先级）
    AppConfig config;
    std::string config_file = "config/v4l2_capture_config.json";

    // 2. JSON配置文件（中等优先级）
    load_config(config_file, config);

    // 3. 命令行参数（最高优先级）
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "-c" || arg == "--config") {
            if (i + 1 < argc) {
                config_file = argv[++i];
                // 重新加载配置文件以确保命令行指定的配置文件优先
                load_config(config_file, config);
            }
        } else if (arg == "-d" || arg == "--device") {
            if (i + 1 < argc) {
                config.device_path = argv[++i];
            }
        } else if (arg == "-w" || arg == "--width") {
            if (i + 1 < argc) {
                config.width = std::stoi(argv[++i]);
            }
        } else if (arg == "-h" || arg == "--height") {
            if (i + 1 < argc) {
                config.height = std::stoi(argv[++i]);
            }
        } else if (arg == "-f" || arg == "--fps") {
            if (i + 1 < argc) {
                config.fps = std::stoi(argv[++i]);
            }
        } else if (arg == "--format") {
            if (i + 1 < argc) {
                std::string format_str = argv[++i];
                config.pixel_format = string_to_pixelformat(format_str);
            }
        } else if (arg == "--topic") {
            if (i + 1 < argc) {
                config.topic_name = argv[++i];
            }
        } else if (arg == "--buffer-count") {
            if (i + 1 < argc) {
                config.buffer_count = std::stoi(argv[++i]);
            }
        } else if (arg == "--no-dma") {
            config.use_dma = false;
        } else if (arg == "--transport") {
            if (i + 1 < argc) {
                std::string transport_str = argv[++i];
                if (transport_str == "FASTDDS") {
                    config.transport_type = TransportType::FASTDDS;
                } else if (transport_str == "DMA") {
                    config.transport_type = TransportType::DMA;
                } else if (transport_str == "SHMEM") {
                    config.transport_type = TransportType::SHMEM;
                }
            }
        } else if (arg == "--help") {
            print_usage(argv[0]);
            return 0;
        }
    }

    LOG_I("=== V4L2 Video Capture ===");
    LOG_I("Device: %s", config.device_path.c_str());
    LOG_I("Resolution: %dx%d", config.width, config.height);
    LOG_I("Pixel Format: %s", V4L2FormatUtils::pixelformat_to_string(config.pixel_format).c_str());
    LOG_I("FPS: %d", config.fps);
    LOG_I("Buffer Count: %d", config.buffer_count);
    LOG_I("Use DMA: %s", config.use_dma ? "Yes" : "No");
    LOG_I("Transport Type: ");
    switch (config.transport_type) {
        case TransportType::FASTDDS:
            LOG_I("FastDDS");
            break;
        case TransportType::DMA:
            LOG_I("DMA");
            break;
        case TransportType::SHMEM:
            LOG_I("Shared Memory");
            break;
    }
    LOG_I("Topic Name: %s", config.topic_name.c_str());

    // Create transport publisher
    TransportConfig transport_config;
    transport_config.type = config.transport_type;
    transport_config.topic_name = config.topic_name;
    
    if (config.transport_type == TransportType::FASTDDS) {
        transport_config.domain_id = 0;
        transport_config.max_samples = 5;
        transport_config.timeout_ms = 1000;
    } else {
        transport_config.buffer_size = V4L2FormatUtils::calculate_frame_size(config.pixel_format, config.width, config.height);
        transport_config.ring_buffer_size = config.buffer_count;
        transport_config.timeout_ms = 1000;
    }

    try {
        g_publisher = VideoTransportFactory::create_publisher(transport_config);
        if (!g_publisher) {
            LOG_E("Failed to create video publisher");
            return 1;
        }
    } catch (const std::exception& e) {
        LOG_E("Exception creating publisher: %s", e.what());
        return 1;
    }

    // Create V4L2 device
    V4L2DeviceConfig v4l2_config;
    v4l2_config.device_path = config.device_path;
    v4l2_config.width = config.width;
    v4l2_config.height = config.height;
    v4l2_config.pixel_format = config.pixel_format;
    v4l2_config.fps = config.fps;
    v4l2_config.buffer_count = config.buffer_count;
    v4l2_config.use_dmabuf = config.use_dma && config.transport_type == TransportType::DMA;

    g_device = V4L2DeviceFactory::create_and_configure(v4l2_config, g_publisher.get());
    if (!g_device) {
        LOG_E("Failed to create and configure V4L2 device");
        g_publisher->cleanup();
        return 1;
    }

    // Start streaming
    if (!g_device->start_streaming()) {
        LOG_E("Failed to start streaming");
        g_device->close_device();
        g_publisher->cleanup();
        return 1;
    }

    LOG_I("Video capture started...");

    // Main capture loop
    uint64_t frame_count = 1;
    const int interval_ms = 1000 / config.fps;
    auto  last_capture_time = std::chrono::steady_clock::now();
    auto prev_30frame_time = last_capture_time;
    const int frames_to_print_fps = 30;

    while (running) {
        V4L2Frame frame;
        if (g_device->capture_frame(frame, 1000)) {
            BufferResult result = g_publisher->publish_buffer(frame.publisher_data);
            if (result == BufferResult::SUCCESS) {
                auto now = std::chrono::steady_clock::now();
                auto capture_interval = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_capture_time).count();
                if (capture_interval < interval_ms) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(interval_ms - capture_interval));
                }
                last_capture_time = now;

                if (frame_count++ % frames_to_print_fps == 0) {
                    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - prev_30frame_time).count();
                    prev_30frame_time = now;

                    if (elapsed > 0) {
                        double fps = static_cast<double>(frames_to_print_fps) / elapsed;
                        LOG_I("v4l2_capture_main: Topic: %s, Frames: %lu, FPS: %.1f", config.topic_name.c_str(), frame_count, fps);
                    }
                }
            } else if (result != BufferResult::TIMEOUT) {
                LOG_E("v4l2_capture_main: Failed to publish frame: %d", static_cast<int>(result));
            }

            g_device->release_frame(frame);
        }
    }

    // Cleanup
    g_device->stop_streaming();
    g_device->close_device();
    g_publisher->cleanup();

    LOG_I("Captured %lu frames", frame_count);
    LOG_I("Video capture stopped.");

    return 0;
}